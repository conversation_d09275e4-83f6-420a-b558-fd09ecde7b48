package cn.iocoder.yudao.module.oa.controller.admin.fuelconsumptioncurve.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.util.*;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 油耗曲线记录 Response VO")
@Data
@ExcelIgnoreUnannotated
public class FuelConsumptionCurveRespVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "15850")
    @ExcelProperty("主键ID")
    private Long id;

    @Schema(description = "车牌号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("车牌号")
    private String vehildno;

    @Schema(description = "车辆所属公司", example = "王五")
    @ExcelProperty("车辆所属公司")
    private String companyName;

    @Schema(description = "时间")
    @ExcelProperty("时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date gpsTimeStr;

    @Schema(description = "当时油量(主油箱)/升")
    @ExcelProperty("当时油量(主油箱)/升")
    private BigDecimal youLiang;

    @Schema(description = "当时油量(副油箱)/升")
    @ExcelProperty("当时油量(副油箱)/升")
    private BigDecimal viceYouLiang;

    @Schema(description = "当时里程，单位米")
    @ExcelProperty("当时里程，单位米")
    private BigDecimal liCheng;

    @Schema(description = "当时位置")
    @ExcelProperty("当时位置")
    private String position;

    @Schema(description = "当时速度; 10 = 1 km/h")
    @ExcelProperty("当时速度km/h")
    private BigDecimal speed;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}