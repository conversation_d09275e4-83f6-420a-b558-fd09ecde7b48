package cn.iocoder.yudao.module.oa.service.socialsecurity;

import cn.hutool.core.collection.CollectionUtil;
import cn.iocoder.yudao.framework.common.exception.ErrorCode;
import cn.iocoder.yudao.framework.common.exception.ServiceException;
import cn.iocoder.yudao.module.oa.controller.admin.incometax.vo.IncomeTaxRespVO;
import cn.iocoder.yudao.module.oa.controller.admin.oilsubsidy.vo.OilSubsidyRespVO;
import cn.iocoder.yudao.module.oa.dal.dataobject.incometax.IncomeTaxDO;
import cn.iocoder.yudao.module.oa.dal.dataobject.monthlysalaryemployees.MonthlySalaryEmployeesDO;
import cn.iocoder.yudao.module.oa.dal.mysql.monthlysalaryemployees.MonthlySalaryEmployeesMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import cn.iocoder.yudao.module.oa.controller.admin.socialsecurity.vo.*;
import cn.iocoder.yudao.module.oa.dal.dataobject.socialsecurity.SocialSecurityDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;

import cn.iocoder.yudao.module.oa.dal.mysql.socialsecurity.SocialSecurityMapper;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.oa.enums.ErrorCodeConstants.*;

/**
 * 社保管理 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SocialSecurityServiceImpl implements SocialSecurityService {

    @Resource
    private SocialSecurityMapper socialSecurityMapper;
    @Resource
    private MonthlySalaryEmployeesMapper monthlySalaryEmployeesMapper;

    @Override
    public Integer createSocialSecurity(SocialSecuritySaveReqVO createReqVO) {
        // 插入
        SocialSecurityDO socialSecurity = BeanUtils.toBean(createReqVO, SocialSecurityDO.class);
        socialSecurityMapper.insert(socialSecurity);
        // 返回
        return socialSecurity.getId();
    }

    @Override
    public void updateSocialSecurity(SocialSecuritySaveReqVO updateReqVO) {
        // 校验存在
        validateSocialSecurityExists(updateReqVO.getId());
        // 更新
        SocialSecurityDO updateObj = BeanUtils.toBean(updateReqVO, SocialSecurityDO.class);
        socialSecurityMapper.updateById(updateObj);
    }

    @Override
    public void deleteSocialSecurity(Integer id) {
        // 校验存在
        validateSocialSecurityExists(id);
        // 删除
        socialSecurityMapper.deleteById(id);
    }

    private void validateSocialSecurityExists(Integer id) {
        if (socialSecurityMapper.selectById(id) == null) {
            throw exception(SOCIAL_SECURITY_NOT_EXISTS);
        }
    }

    @Override
    public SocialSecurityDO getSocialSecurity(Integer id) {
        return socialSecurityMapper.selectById(id);
    }

    @Override
    public PageResult<SocialSecurityRespVO> getSocialSecurityPage(SocialSecurityPageReqVO pageReqVO) {
        IPage<SocialSecurityRespVO> iPage = socialSecurityMapper.selectSocialSecurityPage(new Page<>(pageReqVO.getPageNo(), pageReqVO.getPageSize()), pageReqVO);

        PageResult<SocialSecurityRespVO> pageResult = new PageResult<>(iPage.getRecords(), iPage.getTotal());


        //排序序号赋值
        // 按 createTime 降序排序
        List<SocialSecurityRespVO> sortedList = pageResult.getList().stream()
                .sorted(Comparator.comparing(SocialSecurityRespVO::getCreateTime).reversed())
                .collect(Collectors.toList());

        // 填充序号（1, 2, 3...）
        for (int i = 0; i < sortedList.size(); i++) {
            sortedList.get(i).setRouteIndex(i + 1);
        }

        // 更新 pageResult 的列表
        pageResult.setList(sortedList);
        return pageResult;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SocialSecurityImportRespVO importBusTripList(List<SocialSecurityImportRespVO> list,Boolean updateSupport) {
        if (CollectionUtil.isEmpty(list)) {
            throw exception(NOT_IMPORT_DATA);
        }

        // 初始化返回对象
        SocialSecurityImportRespVO importResult = new SocialSecurityImportRespVO();
        importResult.setCreateUsernames(new ArrayList<>());
        importResult.setUpdateUsernames(new ArrayList<>());
        importResult.setFailureUsernames(new LinkedHashMap<>());

        // 去重处理
        list = list.stream().distinct().collect(Collectors.toList());

        for (SocialSecurityImportRespVO importVO : list) {
            try {
                // 基本校验
                if (importVO.getIdentity() == null) {
                    throw new ServiceException(new ErrorCode(2_100_004, "身份证不可以为空"));
                }

                // 查询员工信息
                MonthlySalaryEmployeesDO employee = monthlySalaryEmployeesMapper.selectByIdCardNumber(importVO.getIdentity());
                if (employee == null) {
                    throw new ServiceException(new ErrorCode(2_100_004,
                            "花名册中不存在该身份证号用户：" + importVO.getIdentity()));
                }

                // 转换为DO对象
                SocialSecurityDO socialSecurityDO = BeanUtils.toBean(importVO, SocialSecurityDO.class);
                socialSecurityDO.setPersonId(Math.toIntExact(employee.getId()));
                socialSecurityDO.setIdentity(employee.getIdCardNumber());

                // 检查是否已存在记录
                SocialSecurityDO existingRecord = socialSecurityMapper.selectOne(
                        SocialSecurityDO::getIdentity, socialSecurityDO.getIdentity(), SocialSecurityDO::getOccurrenceTime, socialSecurityDO.getOccurrenceTime());

                if (existingRecord != null) {
                    if (!updateSupport) {
                        throw new ServiceException(new ErrorCode(2_100_004,
                                employee.getName() + " 已有社保记录，且未勾选导入更新选项"));
                    }
                    // 更新记录
                    socialSecurityDO.setId(existingRecord.getId());
                    socialSecurityMapper.updateById(socialSecurityDO);
                    importResult.getUpdateUsernames().add(employee.getName());
                } else {
                    // 新增记录
                    socialSecurityMapper.insert(socialSecurityDO);
                    importResult.getCreateUsernames().add(employee.getName());
                }
            } catch (ServiceException e) {
                String userName = importVO.getPersonName() != null ?
                        importVO.getPersonName() : "身份证号:" + importVO.getIdentity();
                importResult.getFailureUsernames().put(userName, e.getMessage());
            }
        }

        return importResult;
    }

    @Override
    public List<SocialSecurityRespVO> getSocialSecurityByPostId(Integer personId) {

        List<SocialSecurityDO> socialSecurityDOS = socialSecurityMapper.selectList(SocialSecurityDO::getPersonId, personId);
        List<SocialSecurityRespVO> bean = BeanUtils.toBean(socialSecurityDOS, SocialSecurityRespVO.class);

        for (SocialSecurityRespVO socialSecurityRespVO : bean) {
            MonthlySalaryEmployeesDO monthlySalaryEmployeesDO1 = monthlySalaryEmployeesMapper.selectById(socialSecurityRespVO.getPersonId());
            socialSecurityRespVO.setPersonName(monthlySalaryEmployeesDO1.getName());
            socialSecurityRespVO.setCompanyName(monthlySalaryEmployeesDO1.getCompanyName());
        }

        return bean;
    }

}
