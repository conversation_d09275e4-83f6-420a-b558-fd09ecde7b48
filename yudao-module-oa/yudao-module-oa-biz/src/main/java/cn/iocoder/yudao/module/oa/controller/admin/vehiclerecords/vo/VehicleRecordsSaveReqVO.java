package cn.iocoder.yudao.module.oa.controller.admin.vehiclerecords.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;
import java.math.BigDecimal;

@Schema(description = "管理后台 - 油量汇总新增/修改 Request VO")
@Data
public class VehicleRecordsSaveReqVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "3672")
    private Long id;

    @Schema(description = "司机信息")
    private String driver;

    @Schema(description = "车牌号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "车牌号不能为空")
    private String vehildno;

    @Schema(description = "车辆所属公司", example = "李四")
    private String companyName;

    @Schema(description = "开始时间(设备上报数据时间)")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date beginTime;

    @Schema(description = "结束时间(设备上报数据时间)")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    @Schema(description = "开始油量/升")
    private BigDecimal startYouLiang;

    @Schema(description = "结束油量/升")
    private BigDecimal endYouLiang;

    @Schema(description = "总里程，单位米")
    private BigDecimal liCheng;

    @Schema(description = "总油耗/升")
    private BigDecimal youLiang;

    @Schema(description = "加油量/升")
    private BigDecimal addYouLiang;

    @Schema(description = "时长/小时")
    private String workTime;

}