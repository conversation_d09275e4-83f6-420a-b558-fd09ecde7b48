package cn.iocoder.yudao.module.oa.controller.admin.vehiclerecords.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.util.*;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 油量汇总 Response VO")
@Data
@ExcelIgnoreUnannotated
public class VehicleRecordsRespVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "3672")
    @ExcelProperty("主键ID")
    private Long id;

    @Schema(description = "司机信息")
    @ExcelProperty("司机信息")
    private String driver;

    @Schema(description = "车牌号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("车牌号")
    private String vehildno;

    @Schema(description = "车辆所属公司", example = "李四")
    @ExcelProperty("车辆所属公司")
    private String companyName;

    @Schema(description = "开始时间(设备上报数据时间)")
    @ExcelProperty("开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date beginTime;

    @Schema(description = "结束时间(设备上报数据时间)")
    @ExcelProperty("结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    @Schema(description = "开始油量/升")
    @ExcelProperty("开始油量/升")
    private BigDecimal startYouLiang;

    @Schema(description = "结束油量/升")
    @ExcelProperty("结束油量/升")
    private BigDecimal endYouLiang;

    @Schema(description = "总里程，单位米")
    @ExcelProperty("总里程，单位米")
    private BigDecimal liCheng;

    @Schema(description = "总油耗/升")
    @ExcelProperty("总油耗/升")
    private BigDecimal youLiang;

    @Schema(description = "加油量/升")
    @ExcelProperty("加油量/升")
    private BigDecimal addYouLiang;

    @Schema(description = "时长/小时")
    @ExcelProperty("时长/小时")
    private String workTime;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}