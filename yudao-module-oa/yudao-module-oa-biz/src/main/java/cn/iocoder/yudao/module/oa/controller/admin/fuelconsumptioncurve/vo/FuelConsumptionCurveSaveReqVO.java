package cn.iocoder.yudao.module.oa.controller.admin.fuelconsumptioncurve.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDateTime;
import java.util.*;
import javax.validation.constraints.*;
import java.math.BigDecimal;

@Schema(description = "管理后台 - 油耗曲线记录新增/修改 Request VO")
@Data
public class FuelConsumptionCurveSaveReqVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "15850")
    private Long id;

    @Schema(description = "车牌号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "车牌号不能为空")
    private String vehildno;

    @Schema(description = "车辆所属公司", example = "王五")
    private String companyName;

    @Schema(description = "时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date gpsTimeStr;

    @Schema(description = "当时油量(主油箱); 100 = 1升")
    private BigDecimal youLiang;

    @Schema(description = "当时油量(副油箱); 100 = 1升")
    private BigDecimal viceYouLiang;

    @Schema(description = "当时里程，单位米")
    private BigDecimal liCheng;

    @Schema(description = "当时位置")
    private String position;

    @Schema(description = "当时速度; 10 = 1 km/h")
    private BigDecimal speed;

}