package cn.iocoder.yudao.module.oa.service.incometax;

import cn.hutool.core.collection.CollectionUtil;
import cn.iocoder.yudao.framework.common.exception.ErrorCode;
import cn.iocoder.yudao.framework.common.exception.ServiceException;
import cn.iocoder.yudao.module.oa.controller.admin.housingfund.vo.HousingFundRespVO;
import cn.iocoder.yudao.module.oa.controller.admin.socialsecurity.vo.SocialSecurityRespVO;
import cn.iocoder.yudao.module.oa.dal.dataobject.housingfund.HousingFundDO;
import cn.iocoder.yudao.module.oa.dal.dataobject.monthlysalaryemployees.MonthlySalaryEmployeesDO;
import cn.iocoder.yudao.module.oa.dal.dataobject.socialsecurity.SocialSecurityDO;
import cn.iocoder.yudao.module.oa.dal.mysql.monthlysalaryemployees.MonthlySalaryEmployeesMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import cn.iocoder.yudao.module.oa.controller.admin.incometax.vo.*;
import cn.iocoder.yudao.module.oa.dal.dataobject.incometax.IncomeTaxDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;

import cn.iocoder.yudao.module.oa.dal.mysql.incometax.IncomeTaxMapper;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.oa.enums.ErrorCodeConstants.*;

/**
 * 个税管理 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class IncomeTaxServiceImpl implements IncomeTaxService {

    @Resource
    private IncomeTaxMapper incomeTaxMapper;
    @Resource
    private MonthlySalaryEmployeesMapper monthlySalaryEmployeesMapper;

    @Override
    public Integer createIncomeTax(IncomeTaxSaveReqVO createReqVO) {
        // 插入
        IncomeTaxDO incomeTax = BeanUtils.toBean(createReqVO, IncomeTaxDO.class);
        incomeTaxMapper.insert(incomeTax);
        // 返回
        return incomeTax.getId();
    }

    @Override
    public void updateIncomeTax(IncomeTaxSaveReqVO updateReqVO) {
        // 校验存在
        validateIncomeTaxExists(updateReqVO.getId());
        // 更新
        IncomeTaxDO updateObj = BeanUtils.toBean(updateReqVO, IncomeTaxDO.class);
        incomeTaxMapper.updateById(updateObj);
    }

    @Override
    public void deleteIncomeTax(Integer id) {
        // 校验存在
        validateIncomeTaxExists(id);
        // 删除
        incomeTaxMapper.deleteById(id);
    }

    private void validateIncomeTaxExists(Integer id) {
        if (incomeTaxMapper.selectById(id) == null) {
            throw exception(INCOME_TAX_NOT_EXISTS);
        }
    }

    @Override
    public IncomeTaxDO getIncomeTax(Integer id) {
        return incomeTaxMapper.selectById(id);
    }

    @Override
    public PageResult<IncomeTaxRespVO> getIncomeTaxPage(IncomeTaxPageReqVO pageReqVO) {
        IPage<IncomeTaxRespVO> iPage = incomeTaxMapper.selectIncomeTaxPage(new Page<>(pageReqVO.getPageNo(), pageReqVO.getPageSize()), pageReqVO);

        PageResult<IncomeTaxRespVO> pageResult = new PageResult<>(iPage.getRecords(), iPage.getTotal());

        //排序序号赋值
        // 按 createTime 降序排序
        List<IncomeTaxRespVO> sortedList = pageResult.getList().stream()
                .sorted(Comparator.comparing(IncomeTaxRespVO::getCreateTime).reversed())
                .collect(Collectors.toList());

        // 填充序号（1, 2, 3...）
        for (int i = 0; i < sortedList.size(); i++) {
            sortedList.get(i).setRouteIndex(i + 1);
        }

        // 更新 pageResult 的列表
        pageResult.setList(sortedList);
        return pageResult;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public IncomeTaxImportRespVO importBusTripList(List<IncomeTaxImportRespVO> list,Boolean updateSupport) {
        if (CollectionUtil.isEmpty(list)) {
            throw exception(NOT_IMPORT_DATA);
        }

        // 初始化返回对象
        IncomeTaxImportRespVO importResult = new IncomeTaxImportRespVO();
        importResult.setCreateUsernames(new ArrayList<>());
        importResult.setUpdateUsernames(new ArrayList<>());
        importResult.setFailureUsernames(new LinkedHashMap<>());

        // 去重处理
        list = list.stream().distinct().collect(Collectors.toList());

        for (IncomeTaxImportRespVO importVO : list) {
            try {
                // 基本校验
                if (importVO.getIdentity() == null) {
                    throw new ServiceException(new ErrorCode(2_100_004, "身份证不可以为空"));
                }

                // 查询员工信息
                MonthlySalaryEmployeesDO employee = monthlySalaryEmployeesMapper.selectByIdCardNumber(importVO.getIdentity());
                if (employee == null) {
                    throw new ServiceException(new ErrorCode(2_100_004,
                            "花名册中不存在该身份证号用户：" + importVO.getIdentity()));
                }

                // 转换为DO对象
                IncomeTaxDO incomeTaxDO = BeanUtils.toBean(importVO, IncomeTaxDO.class);
                incomeTaxDO.setPersonId(Math.toIntExact(employee.getId()));
                incomeTaxDO.setCompanyName(employee.getCompanyName());

                // 检查是否已存在记录
                IncomeTaxDO existingRecord = incomeTaxMapper.selectOne(IncomeTaxDO::getIdentity, incomeTaxDO.getIdentity(), IncomeTaxDO::getOccurrenceTime, incomeTaxDO.getOccurrenceTime());

                if (existingRecord != null) {
                    if (!updateSupport) {
                        throw new ServiceException(new ErrorCode(2_100_004,
                                employee.getName() + " 已有记录，且未勾选导入更新选项"));
                    }
                    // 更新记录
                    incomeTaxDO.setId(existingRecord.getId());
                    incomeTaxMapper.updateById(incomeTaxDO);
                    importResult.getUpdateUsernames().add(employee.getName());
                } else {
                    // 新增记录
                    incomeTaxMapper.insert(incomeTaxDO);
                    importResult.getCreateUsernames().add(employee.getName());
                }
            } catch (ServiceException e) {
                importResult.getFailureUsernames().put(
                        importVO.getPersonName() != null ? importVO.getPersonName() : "未知用户",
                        e.getMessage());
            }
        }

        return importResult;
    }

    @Override
    public List<IncomeTaxRespVO> getIncomeTaxByPostId(Integer personId) {

        List<IncomeTaxDO> housingFundDOS = incomeTaxMapper.selectList(IncomeTaxDO::getPersonId, personId);
        List<IncomeTaxRespVO> bean = BeanUtils.toBean(housingFundDOS, IncomeTaxRespVO.class);

        for (IncomeTaxRespVO incomeTaxRespVO : bean) {
            MonthlySalaryEmployeesDO monthlySalaryEmployeesDO1 = monthlySalaryEmployeesMapper.selectById(incomeTaxRespVO.getPersonId());
            incomeTaxRespVO.setPersonName(monthlySalaryEmployeesDO1.getName());
        }

        return bean;
    }

}
