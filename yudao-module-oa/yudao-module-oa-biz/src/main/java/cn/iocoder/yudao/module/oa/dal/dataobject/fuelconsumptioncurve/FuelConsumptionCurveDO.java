package cn.iocoder.yudao.module.oa.dal.dataobject.fuelconsumptioncurve;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;
import java.util.*;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 油耗曲线记录 DO
 *
 * <AUTHOR>
 */
@TableName("t_fuel_consumption_curve")
@KeySequence("t_fuel_consumption_curve_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FuelConsumptionCurveDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;
    /**
     * 车牌号
     */
    private String vehildno;
    /**
     * 车辆所属公司
     */
    private String companyName;
    /**
     * 时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date gpsTimeStr;
    /**
     * 当时油量(主油箱); 100 = 1升
     */
    private BigDecimal youLiang;
    /**
     * 当时油量(副油箱); 100 = 1升
     */
    private BigDecimal viceYouLiang;
    /**
     * 当时里程，单位米
     */
    private BigDecimal liCheng;
    /**
     * 当时位置
     */
    private String position;
    /**
     * 当时速度; 10 = 1 km/h
     */
    private BigDecimal speed;

}