package cn.iocoder.yudao.module.oa.service.housingfund;

import cn.hutool.core.collection.CollectionUtil;
import cn.iocoder.yudao.framework.common.exception.ErrorCode;
import cn.iocoder.yudao.framework.common.exception.ServiceException;
import cn.iocoder.yudao.module.oa.controller.admin.driversalary.vo.DriverSalaryPageReqVO;
import cn.iocoder.yudao.module.oa.controller.admin.housingfund.vo.HousingFundImportRepVO;
import cn.iocoder.yudao.module.oa.controller.admin.housingfund.vo.HousingFundPageReqVO;
import cn.iocoder.yudao.module.oa.controller.admin.housingfund.vo.HousingFundRespVO;
import cn.iocoder.yudao.module.oa.controller.admin.housingfund.vo.HousingFundSaveReqVO;
import cn.iocoder.yudao.module.oa.controller.admin.incometax.vo.IncomeTaxRespVO;
import cn.iocoder.yudao.module.oa.dal.dataobject.caregiversalary.CaregiverSalaryDO;
import cn.iocoder.yudao.module.oa.dal.dataobject.housingfund.HousingFundDO;
import cn.iocoder.yudao.module.oa.dal.dataobject.incometax.IncomeTaxDO;
import cn.iocoder.yudao.module.oa.dal.dataobject.monthlysalaryemployees.MonthlySalaryEmployeesDO;
import cn.iocoder.yudao.module.oa.dal.dataobject.socialsecurity.SocialSecurityDO;
import cn.iocoder.yudao.module.oa.dal.mysql.housingfund.HousingFundMapper;
import cn.iocoder.yudao.module.oa.dal.mysql.monthlysalaryemployees.MonthlySalaryEmployeesMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.oa.enums.ErrorCodeConstants.HOUSING_FUND_NOT_EXISTS;
import static cn.iocoder.yudao.module.oa.enums.ErrorCodeConstants.NOT_IMPORT_DATA;

/**
 * 公积金管理 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class HousingFundServiceImpl implements HousingFundService {

    @Resource
    private HousingFundMapper housingFundMapper;
    @Resource
    private MonthlySalaryEmployeesMapper monthlySalaryEmployeesMapper;


    @Override
    public Integer createHousingFund(HousingFundSaveReqVO createReqVO) {

        MonthlySalaryEmployeesDO monthlySalaryEmployeesDO = monthlySalaryEmployeesMapper.selectByIdCardNumber(createReqVO.getIdentity());

        // 插入
        HousingFundDO housingFund = BeanUtils.toBean(createReqVO, HousingFundDO.class);
        housingFund.setPersonId(Math.toIntExact(monthlySalaryEmployeesDO.getId()));
        housingFundMapper.insert(housingFund);
        // 返回
        return housingFund.getId();
    }

    @Override
    public void updateHousingFund(HousingFundSaveReqVO updateReqVO) {
        // 校验存在
        validateHousingFundExists(updateReqVO.getId());


        MonthlySalaryEmployeesDO monthlySalaryEmployeesDO = monthlySalaryEmployeesMapper.selectByIdCardNumber(updateReqVO.getIdentity());

        // 更新
        HousingFundDO updateObj = BeanUtils.toBean(updateReqVO, HousingFundDO.class);
        updateObj.setPersonId(Math.toIntExact(monthlySalaryEmployeesDO.getId()));
        housingFundMapper.updateById(updateObj);
    }

    @Override
    public void deleteHousingFund(Integer id) {
        // 校验存在
        validateHousingFundExists(id);
        // 删除
        housingFundMapper.deleteById(id);
    }

    private void validateHousingFundExists(Integer id) {
        if (housingFundMapper.selectById(id) == null) {
            throw exception(HOUSING_FUND_NOT_EXISTS);
        }
    }

    @Override
    public HousingFundRespVO getHousingFund(Integer id) {
        HousingFundDO housingFundDO = housingFundMapper.selectById(id);
        MonthlySalaryEmployeesDO monthlySalaryEmployeesDO = monthlySalaryEmployeesMapper.selectByIdCardNumber(housingFundDO.getIdentity());
        HousingFundRespVO housingFundRespVO = BeanUtils.toBean(housingFundDO, HousingFundRespVO.class);
        housingFundRespVO.setPersonName(monthlySalaryEmployeesDO.getName());
        return housingFundRespVO;
    }

    @Override
    public PageResult<HousingFundRespVO> getHousingFundPage(HousingFundPageReqVO pageReqVO) {
        IPage<HousingFundRespVO> iPage = housingFundMapper.selectHousingFundPage(new Page<>(pageReqVO.getPageNo(), pageReqVO.getPageSize()), pageReqVO);

        PageResult<HousingFundRespVO> pageResult = new PageResult<>(iPage.getRecords(), iPage.getTotal());

        //排序序号赋值
        // 按 createTime 降序排序
        List<HousingFundRespVO> sortedList = pageResult.getList().stream()
                .sorted(Comparator.comparing(HousingFundRespVO::getCreateTime).reversed())
                .collect(Collectors.toList());

        // 填充序号（1, 2, 3...）
        for (int i = 0; i < sortedList.size(); i++) {
            sortedList.get(i).setRouteIndex(i + 1);
        }

        // 更新 pageResult 的列表
        pageResult.setList(sortedList);
        return pageResult;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public HousingFundImportRepVO importBusTripList(List<HousingFundImportRepVO> list,Boolean updateSupport) {
        if (CollectionUtil.isEmpty(list)) {
            throw exception(NOT_IMPORT_DATA);
        }

        // 初始化返回对象
        HousingFundImportRepVO importResult = new HousingFundImportRepVO();
        importResult.setCreateUsernames(new ArrayList<>());
        importResult.setUpdateUsernames(new ArrayList<>());
        importResult.setFailureUsernames(new LinkedHashMap<>());

        // 去重处理
        list = list.stream().distinct().collect(Collectors.toList());

        for (HousingFundImportRepVO importVO : list) {
            try {
                // 基本校验
                if (importVO.getIdentity() == null) {
                    throw new ServiceException(new ErrorCode(2_100_004, "身份证不可以为空"));
                }

                // 查询员工信息
                MonthlySalaryEmployeesDO employee = monthlySalaryEmployeesMapper.selectByIdCardNumber(importVO.getIdentity());
                if (employee == null) {
                    throw new ServiceException(new ErrorCode(2_100_004,
                            "花名册中不存在该身份证号用户：" + importVO.getIdentity()));
                }

                // 转换为DO对象
                HousingFundDO housingFundDO = BeanUtils.toBean(importVO, HousingFundDO.class);
                housingFundDO.setPersonId(Math.toIntExact(employee.getId()));
                housingFundDO.setCompanyName(employee.getCompanyName());

                // 检查是否已存在记录
                HousingFundDO existingRecord = housingFundMapper.selectOne(
                        HousingFundDO::getIdentity, housingFundDO.getIdentity(),HousingFundDO::getOccurrenceTime, housingFundDO.getOccurrenceTime());

                if (existingRecord != null) {
                    if (!updateSupport) {
                        throw new ServiceException(new ErrorCode(2_100_004,
                                employee.getName() + " 已有公积金记录，且未勾选导入更新选项"));
                    }
                    // 更新记录
                    housingFundDO.setId(existingRecord.getId());
                    housingFundMapper.updateById(housingFundDO);
                    importResult.getUpdateUsernames().add(employee.getName());
                } else {
                    // 新增记录
                    housingFundMapper.insert(housingFundDO);
                    importResult.getCreateUsernames().add(employee.getName());
                }
            } catch (ServiceException e) {
                String userName = importVO.getName() != null ?
                        importVO.getName() : "身份证号:" + importVO.getIdentity();
                importResult.getFailureUsernames().put(userName, e.getMessage());
            }
        }

        return importResult;
    }

    @Override
    public List<HousingFundRespVO> getHousingByPostId(Integer personId) {
        List<HousingFundDO> housingFundDOS = housingFundMapper.selectList(HousingFundDO::getPersonId,personId);
        List<HousingFundRespVO> bean = BeanUtils.toBean(housingFundDOS, HousingFundRespVO.class);

        for (HousingFundRespVO housingFundRespVO : bean) {
            MonthlySalaryEmployeesDO monthlySalaryEmployeesDO1 = monthlySalaryEmployeesMapper.selectById(housingFundRespVO.getPersonId());
            housingFundRespVO.setPersonName(monthlySalaryEmployeesDO1.getName());
        }

        return bean;
    }


}
